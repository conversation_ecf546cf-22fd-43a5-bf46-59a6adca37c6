apiVersion: v1
kind: ConfigMap
metadata:
  name: traefik-config
  namespace: default
data:
  traefik.yml: |
    global:
      checkNewVersion: false
      sendAnonymousUsage: false

    api:
      dashboard: true
      debug: true
      insecure: true
      disableDashboardAd: true

    ping: {}

    entryPoints:
      web:
        address: ":80"
      websecure:
        address: ":443"
      video:
        address: ":10000/udp"

    certificatesResolvers:
      letsencrypt:
        acme:
          email: <EMAIL>
          storage: /data/acme.json
          httpChallenge:
            entryPoint: web

    providers:
      kubernetesIngress: {}
      kubernetesCRD:
        allowCrossNamespace: true

    log:
      level: INFO
      filePath: /var/log/traefik/traefik.log

    accessLog:
      filePath: /var/log/traefik/access.log
