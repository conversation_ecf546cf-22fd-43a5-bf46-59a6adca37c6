#!/usr/bin/env bash
set -euo pipefail

NAMESPACE=jitsi

echo ">> Thiế<PERSON> lập KUBECONFIG"
export KUBECONFIG=/Users/<USER>/code/projects/2025/k8s-deployment/.kube/config

echo ">> Kiểm tra trạng thái pods"
kubectl get pods -n $NAMESPACE

echo ""
echo ">> Kiểm tra services"
kubectl get svc -n $NAMESPACE

echo ""
echo ">> Kiểm tra cấu hình domain trong ConfigMap"
echo "XMPP Domains:"
kubectl get configmap jitsi-prosody-common -n $NAMESPACE -o yaml | grep -E "(XMPP_DOMAIN|XMPP_AUTH_DOMAIN|XMPP_MUC_DOMAIN|PUBLIC_URL)"

echo ""
echo ">> Kiểm tra config.js từ web"
echo "JavaScript Config:"
curl -s http://jitsi.osp.vn:32032/config.js | grep -E "(hosts.domain|focusUserJid|hosts.muc)" | head -3

echo ""
echo ">> Kiểm tra kết nối HTTP"
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://jitsi.osp.vn:32032)
if [ "$HTTP_STATUS" = "200" ]; then
    echo "✅ HTTP connection: OK ($HTTP_STATUS)"
else
    echo "❌ HTTP connection: FAILED ($HTTP_STATUS)"
fi

echo ""
echo ">> Kiểm tra logs Jicofo (lỗi gần đây)"
echo "Recent errors in Jicofo:"
if kubectl logs -n $NAMESPACE -l app.kubernetes.io/component=jicofo --tail=50 | grep -E "(SEVERE|ERROR|Failed)" | tail -3; then
    echo "⚠️  Có lỗi trong Jicofo logs"
else
    echo "✅ Không có lỗi SEVERE/ERROR trong Jicofo logs gần đây"
fi

echo ""
echo ">> Kiểm tra logs Prosody (authentication)"
echo "Recent authentications:"
kubectl logs -n $NAMESPACE jitsi-prosody-0 --tail=20 | grep -E "(Authenticated|auth)" | tail -3 || echo "Không tìm thấy log authentication gần đây"

echo ""
echo ">> Tóm tắt trạng thái"
echo "🌐 Web Interface: http://jitsi.osp.vn:32032"
echo "🔧 Domain: jitsi.osp.vn"
echo "📊 Dashboard Traefik: http://192.168.1.96:8080"

echo ""
echo ">> Hướng dẫn test"
echo "1. Mở trình duyệt: http://jitsi.osp.vn:32032"
echo "2. Tạo phòng họp mới"
echo "3. Cho phép truy cập microphone và camera khi được hỏi"
echo "4. Kiểm tra audio/video hoạt động"
echo "5. Mời người khác tham gia để test"
