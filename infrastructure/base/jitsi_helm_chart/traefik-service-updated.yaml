apiVersion: v1
kind: Service
metadata:
  labels:
    app: traefik
  name: traefik
  namespace: default
spec:
  ports:
  - name: web
    port: 80
    targetPort: 80
    nodePort: 32032
    protocol: TCP
  - name: websecure
    port: 443
    targetPort: 443
    nodePort: 31576
    protocol: TCP
  - name: video
    port: 10000
    targetPort: 10000
    nodePort: 31000
    protocol: UDP
  selector:
    app: traefik
  type: LoadBalancer
