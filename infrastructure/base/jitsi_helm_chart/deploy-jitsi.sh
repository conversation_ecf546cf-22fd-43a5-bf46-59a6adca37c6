#!/usr/bin/env bash
set -euo pipefail

NAMESPACE=jitsi
HELM_REPO="https://jitsi-contrib.github.io/jitsi-helm"

echo ">> Thi<PERSON><PERSON> lập KUBECONFIG"
export KUBECONFIG=/Users/<USER>/code/projects/2025/k8s-deployment/.kube/config

echo ">> Tạo namespace"
kubectl create ns $NAMESPACE || true

echo ">> Thê<PERSON> Helm repo"
helm repo add jitsi $HELM_REPO
helm repo update

echo ">> Kiểm tra CRD IngressRouteUDP"
kubectl get crd ingressrouteudps.traefik.io || echo "CRD IngressRouteUDP không tồn tại, cần cài đặt thủ công"

echo ">> Cài đặt Jitsi"
helm install jitsi jitsi/jitsi-meet \
  --namespace $NAMESPACE \
  -f values-jitsi.yaml \
  --version 1.3.7 \
  --wait

echo ">> Tạo IngressRoute và IngressRouteUDP"
cat <<'EOF' | kubectl apply -f -
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: jitsi-web
  namespace: jitsi
spec:
  entryPoints:
    - web
  routes:
  - match: Host(`jitsi.osp.vn`)
    kind: Rule
    services:
    - name: jitsi-jitsi-meet-web
      port: 80
---
apiVersion: traefik.io/v1alpha1
kind: IngressRouteUDP
metadata:
  name: jitsi-jvb
  namespace: jitsi
spec:
  entryPoints:
    - video
  routes:
  - services:
    - name: jitsi-jitsi-meet-jvb
      port: 31001
EOF
echo ">> Hoàn tất!"
