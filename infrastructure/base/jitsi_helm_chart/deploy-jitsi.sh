#!/usr/bin/env bash
set -euo pipefail

NAMESPACE=jitsi
HELM_REPO="https://jitsi-contrib.github.io/jitsi-helm"

echo ">> Thi<PERSON><PERSON> lập KUBECONFIG"
export KUBECONFIG=/Users/<USER>/code/projects/2025/k8s-deployment/.kube/config

echo ">> Tạo namespace"
kubectl create ns $NAMESPACE || true

echo ">> Thê<PERSON> <PERSON><PERSON> repo"
helm repo add jitsi $HELM_REPO
helm repo update

echo ">> Kiểm tra CRD IngressRouteUDP"
kubectl get crd ingressrouteudps.traefik.io || echo "CRD IngressRouteUDP không tồn tại, cần cài đặt thủ công"

echo ">> Cài đặt/Cập nhật Jitsi"
helm upgrade --install jitsi jitsi/jitsi-meet \
  --namespace $NAMESPACE \
  -f values-jitsi.yaml \
  --version 1.3.7 \
  --wait

echo ">> Tạo IngressRoute và IngressRouteUDP"
cat <<'EOF' | kubectl apply -f -
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: jitsi-web
  namespace: jitsi
spec:
  entryPoints:
    - web
  routes:
  - match: Host(`jitsi.osp.vn`)
    kind: Rule
    services:
    - name: jitsi-jitsi-meet-web
      port: 80
---
apiVersion: traefik.io/v1alpha1
kind: IngressRouteUDP
metadata:
  name: jitsi-jvb
  namespace: jitsi
spec:
  entryPoints:
    - video
  routes:
  - services:
    - name: jitsi-jitsi-meet-jvb
      port: 31001
EOF

echo ">> Đợi tất cả pod sẵn sàng"
kubectl wait --for=condition=ready pod --all -n $NAMESPACE --timeout=120s

echo ">> Restart Prosody để áp dụng cấu hình domain mới"
kubectl delete pod jitsi-prosody-0 -n $NAMESPACE
kubectl wait --for=condition=ready pod jitsi-prosody-0 -n $NAMESPACE --timeout=120s

echo ">> Restart Web pod để áp dụng cấu hình mới"
kubectl delete pod -l app.kubernetes.io/component=web -n $NAMESPACE
kubectl wait --for=condition=ready pod -l app.kubernetes.io/component=web -n $NAMESPACE --timeout=120s

echo ">> Kiểm tra trạng thái"
kubectl get pods -n $NAMESPACE

echo ">> Kiểm tra cấu hình domain"
echo "Domain trong ConfigMap:"
kubectl get configmap jitsi-prosody-common -n $NAMESPACE -o yaml | grep -E "(XMPP_DOMAIN|XMPP_AUTH_DOMAIN|XMPP_MUC_DOMAIN)"

echo ">> Kiểm tra config.js"
echo "Domain trong config.js:"
curl -s http://jitsi.osp.vn:32032/config.js | grep -E "(hosts.domain|focusUserJid)" | head -2

echo ">> Hoàn tất! Jitsi Meet đã sẵn sàng tại: http://jitsi.osp.vn:32032"
