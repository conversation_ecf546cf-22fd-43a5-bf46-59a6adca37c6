publicURL: "https://jitsi.osp.vn:32032"

# C<PERSON><PERSON> hình domain XMPP
xmpp:
  domain: "jitsi.osp.vn"
  authDomain: "auth.jitsi.osp.vn"
  mucDomain: "muc.jitsi.osp.vn"
  internalMucDomain: "internal-muc.jitsi.osp.vn"
  guestDomain: "guest.jitsi.osp.vn"
  recorderDomain: "recorder.jitsi.osp.vn"

web:
  service:
    type: ClusterIP
  websocket:
    enabled: true

jvb:
  # Expose cổng media qua NodePort 31000
  service:
    type: NodePort
  nodePort: 31001
  publicIPs:
    - "************"
  UDPPort: 31001
  websocket:
    enabled: true

prosody:
  service:
    type: ClusterIP

jicofo:
  service:
    type: ClusterIP

ingress:
  enabled: false  # dùng Traefik CRDs thay vì Ingress

traefik:
  enabled: true   # tạo CRD UDP cho JVB
