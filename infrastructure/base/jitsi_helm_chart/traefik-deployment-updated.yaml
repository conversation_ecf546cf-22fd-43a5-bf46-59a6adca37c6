apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: traefik
  name: traefik
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: traefik
  template:
    metadata:
      labels:
        app: traefik
    spec:
      serviceAccountName: traefik
      containers:
      - name: traefik
        image: traefik:v3.0
        args:
        - --configfile=/config/traefik.yml
        ports:
        - containerPort: 80
          name: web
          protocol: TCP
        - containerPort: 443
          name: websecure
          protocol: TCP
        - containerPort: 8080
          name: dashboard
          protocol: TCP
        - containerPort: 10000
          name: video
          protocol: UDP
        livenessProbe:
          httpGet:
            path: /ping
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ping
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: config
          mountPath: /config
        - name: data
          mountPath: /data
        - name: logs
          mountPath: /var/log/traefik
      volumes:
      - name: config
        configMap:
          name: traefik-config
      - name: data
        persistentVolumeClaim:
          claimName: traefik-data
      - name: logs
        emptyDir: {}
