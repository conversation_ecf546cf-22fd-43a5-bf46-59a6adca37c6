<img src="https://r2cdn.perplexity.ai/pplx-full-logo-primary-dark%402x.png" class="logo" width="120"/>

# Hướng dẫn triển khai Jitsi Meet trên Kubernetes với Traefik

Trọng tâm của tài liệu này là thiết lập mộtm **Kubernetes** (K8s) chạy **Jitsi Meet** sau cổng **Traefik** đã sẵn sàng tại `************:32032` và xuất bản dịch vụ qua tên miền `https://jitsi.ops.vn:32032`. Hướng dẫn bao gồm:

* Chuẩn bị hạ tầng và chứng chỉ
* Tùy chỉnh Traefik cho TCP-HTTPS \& UDP
* Cài đặt Jitsi bằng Helm và script tự động
* Kiểm tra sau triển khai \& xử lý sự cố

![Sơ đồ kiến trúc <PERSON>i trên Kubernetes với Traefik](https://user-gen-media-assets.s3.amazonaws.com/gpt4o_images/fa562235-95c8-4f7c-8b7a-b8e6d995ab4e.png)

Sơ đồ kiến trúc Jitsi trên Kubernetes với Traefik

## 1. Điều kiện tiền đề

### 1.1 Cụm Kubernetes

* K8s ≥ v1.24 cùng plugin CNI hỗ trợ LoadBalancer/NodePort.  [^1]
* `kubectl`, `helm` ≥ v3.12 trên máy quản trị.
* Namespace mặc định `jitsi`.


### 1.2 Traefik Gateway

Traefik đã chạy dạng **Deployment** trong namespace `traefik` và expose NodePort `32032`. Bảo đảm:

```yaml
# traefik-values.yaml (trích yếu)
ports:
  web:
    port: 80
  websecure:
    port: 443
  video:            # entrypoint cho UDP 10000
    port: 10000
    protocol: UDP
entryPoints:
  video:
    address: ":10000/udp"
```

Cách khai báo entrypoint \& IngressRouteUDP lấy từ hướng dẫn chính thức Traefik.[^2][^3]

### 1.3 DNS \& TLS

* Bản ghi `A` của `jitsi.ops.vn` trỏ tới `************`.
* Nếu chưa có chứng chỉ, có thể bắt đầu bằng **self-signed** rồi chuyển sang Let’s Encrypt ACME thông qua Traefik later.[^4]


## 2. Chuẩn bị giá trị Helm

Tạo file `values-jitsi.yaml` dựa trên chart **jitsi-helm** mới nhất:[^5][^6]

```yaml
global:
  publicURL: "https://jitsi.ops.vn:32032"

web:
  service:
    type: ClusterIP

jvb:
  # Expose cổng media qua NodePort 31000
  service:
    type: NodePort
    nodePorts:
      udp: 31000
  extraEnvs:
    - name: JVB_ADVERTISE_PORT
      value: "31000"
    - name: JVB_PORT
      value: "31000"
    - name: DOCKER_HOST_ADDRESS
      value: "************"

ingress:
  enabled: false  # dùng Traefik CRDs thay vì Ingress

traefik:
  enabled: true   # tạo CRD UDP cho JVB
```


## 3. Script triển khai tự động

Tạo tập tin `deploy-jitsi.sh`:

```bash
#!/usr/bin/env bash
set -euo pipefail

NAMESPACE=jitsi
HELM_REPO="https://jitsi-contrib.github.io/jitsi-helm"

echo ">> Tạo namespace"
kubectl create ns $NAMESPACE || true

echo ">> Thêm Helm repo"
helm repo add jitsi $HELM_REPO
helm repo update

echo ">> Cài đặt CRD IngressRouteUDP"
kubectl apply -f https://raw.githubusercontent.com/traefik/traefik/v3.0/docs/content/reference/ingressrouteudp/crd-definition.yml

echo ">> Cài đặt Jitsi"
helm install jitsi jitsi/jitsi-helm \
  --namespace $NAMESPACE \
  -f values-jitsi.yaml \
  --version 1.3.8 \
  --wait

echo ">> Tạo IngressRoute và IngressRouteUDP"
cat <<'EOF' | kubectl apply -f -
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: jitsi-web
  namespace: jitsi
spec:
  entryPoints:
    - websecure
  routes:
  - match: Host(`jitsi.ops.vn`)
    kind: Rule
    services:
    - name: jitsi-web
      port: 80
  tls:
    insecureSkipVerify: true         # self-signed giai đoạn đầu
---
apiVersion: traefik.io/v1alpha1
kind: IngressRouteUDP
metadata:
  name: jitsi-jvb
  namespace: jitsi
spec:
  entryPoints:
    - video
  routes:
  - services:
    - name: jitsi-jvb
      port: 31000
      protocol: UDP
EOF
echo ">> Hoàn tất!"
```

*Script* cài đặt CRD UDP, chart Jitsi và khai báo hai route—HTTP và UDP—cho Traefik. NodePort `31000/udp` nằm trong khoảng hợp lệ K8s.[^7]

## 4. Thực hiện triển khai

```bash
chmod +x deploy-jitsi.sh
./deploy-jitsi.sh
```

Helm sẽ kéo images mới nhất (`web`, `prosody`, `jicofo`, `jvb`) và đợi toàn bộ pod sẵn sàng.[^8][^9]

## 5. Kiểm tra sau triển khai

1. **Pods \& Services**
```bash
kubectl get pods,svc -n jitsi
```

Tất cả pod phải ở trạng thái `Running` và service `jitsi-jvb` hiển thị `UDP:31000`.[^10]

2. **Traefik Dashboard**

Xác nhận router `jitsi-web` (HTTPS) và router UDP `jitsi-jvb` xuất hiện, trạng thái `Healthy`.[^11]

3. **Truy cập thử**

Mở trình duyệt: `https://jitsi.ops.vn:32032`. Tạo phòng với ≥ 3 người để ép luồng media qua JVB; nếu video/âm thanh đầy đủ, thiết lập đã thành công.[^9]

## 6. Điều chỉnh \& tối ưu sau cài đặt

### 6.1 Bật WebSocket

Thêm vào `values-jitsi.yaml`:

```yaml
web:
  websocket:
    enabled: true
jvb:
  websocket:
    enabled: true
```

Giúp giảm độ trễ cho client mới nhất.[^12][^13]

### 6.2 Chuyển sang Let’s Encrypt

Bật ACME trong Traefik:

```yaml
certificatesResolvers:
  letsencrypt:
    acme:
      email: "[email protected]"
      storage: "/data/acme.json"
      httpChallenge:
        entryPoint: web
```

Sau đó xoá `insecureSkipVerify` khỏi IngressRoute để dùng TLS hợp lệ.[^4]

### 6.3 Scale JVB

```bash
helm upgrade jitsi jitsi/jitsi-helm -n jitsi \
  --set jvb.replicas=3 \
  -f values-jitsi.yaml
```

Traefik UDP sẽ cân bằng Round-Robin giữa 3 service backend nhờ `nativeLB`.[^3][^14]

## 7. Khắc phục lỗi thường gặp

| Triệu chứng | Nguyên nhân | Cách khắc phục |
| :-- | :-- | :-- |
| **Không thấy hình khi có người thứ 3** | UDP 10000 bị chặn hoặc sai port | Kiểm tra firewall mở `31000/udp`; xác nhận `JVB_ADVERTISE_PORT` khớp `nodePort`[^7][^15] |
| **WebSocket 403** | Thiếu rule Colibri hoặc Prosody WS | Bật `websocket.enabled` như mục 6.1 và reload[^12][^16] |
| **404 từ Traefik** | Thiếu router hoặc host rule | Kiểm tra label/route; xóa cache \& redeploy Traefik CRDs[^11] |

## 8. Kết luận

Với cấu hình Helm linh hoạt và Traefik hỗ trợ cả HTTPS lẫn UDP, việc đưa **Jitsi Meet** vào cụm **Kubernetes** trở nên gọn gàng, dễ mở rộng. Bạn có thể tiếp tục:

* Bật JWT để kiểm soát phòng họp.  [^17]
* Triển khai Prometheus exporter cho JVB để giám sát.  [^18]
* Thêm Jibri/Jigasi nếu cần ghi hình hoặc PSTN.

Thực hiện đúng các bước trên, hệ thống sẽ hoạt động ổn định, tránh lỗi cấu hình cổng, WebSocket hay giả lập NAT.

<div style="text-align: center">⁂</div>

[^1]: https://jitsi.github.io/handbook/docs/devops-guide/devops-guide-scalable/

[^2]: https://doc.traefik.io/traefik/routing/entrypoints/

[^3]: https://doc.traefik.io/traefik-hub/api-gateway/reference/routing/kubernetes/udp/routers/ref-ingressrouteudp

[^4]: https://github.com/jitsi/docker-jitsi-meet/issues/358

[^5]: https://github.com/jitsi-contrib/jitsi-helm

[^6]: https://github.com/jitsi-contrib/jitsi-helm/blob/main/values.yaml

[^7]: https://cloud.tencent.com/developer/ask/sof/*********

[^8]: https://artifacthub.io/packages/helm/jitsi-meet/jitsi-meet/1.3.8

[^9]: https://www.codeslikeaduck.com/posts/jitsiwithtraefik/

[^10]: https://sesamedisk.com/video-conferencing-with-jitsi-on-k8s/

[^11]: https://github.com/jitsi/docker-jitsi-meet/issues/1113

[^12]: https://github.com/jitsi/docker-jitsi-meet/issues/1650

[^13]: https://jitsi.github.io/handbook/docs/devops-guide/faq/

[^14]: https://element-hq.github.io/jitsi-helm/

[^15]: https://github.com/jitsi/docker-jitsi-meet/issues/151

[^16]: https://github.com/jitsi/docker-jitsi-meet/issues/880

[^17]: https://jitsi.github.io/handbook/docs/devops-guide/token-authentication/

[^18]: https://grafana.com/grafana/dashboards/15848-jitsi-meet-system/

[^19]: https://artifacthub.io/packages/helm/jitsi-meet/jitsi-meet/1.3.1

[^20]: https://github.com/element-hq/jitsi-helm

[^21]: https://gitlab.opencode.de/bmi/opendesk/components/platform-development/charts/opendesk-jitsi

[^22]: https://www.peerspot.com/products/comparisons/bitnami-helm-chart-for-concourse_vs_jitsi-meet-500-user-video-conferencing-server-supported-by-meetrix

[^23]: https://github.com/jitsi-contrib/jitsi-kubernetes

[^24]: https://doc.traefik.io/traefik/providers/kubernetes-ingress/

[^25]: https://forge.tedomum.net/tedomum/helm-charts/-/blob/46b37840b653aa49e436c61cd4c2e8576a74c182/jitsi/Chart.yaml

[^26]: https://stackoverflow.com/questions/********/how-to-setup-jitsi-meet-on-a-custom-kubernetes

[^27]: https://community.traefik.io/t/traefik-not-routing-jitsi-correctly/11599

[^28]: https://docs.l7mp.io/en/latest/examples/jitsi/

[^29]: https://readthedocs.vinczejanos.info/Blog/2022/10/17/jitsi_on_kubernetes/

[^30]: https://meetrix.io/store/sq-lite/

[^31]: https://gitlab.breakglass.io/shared/talos/-/blob/9f5811564f4d29440d2e82b783f8e4cabf39162a/workloads/jitsi/ingress.yaml

[^32]: https://jitsi.github.io/handbook/docs/category/deployment/

[^33]: https://community.traefik.io/t/traefik-not-routing-traffic-to-backend-service/25368

[^34]: https://github.com/jitsi/docker-jitsi-meet/issues/565

[^35]: https://jitsi.github.io/handbook/docs/devops-guide/devops-guide-docker/

[^36]: https://github.com/hpi-schul-cloud/jitsi-deployment

[^37]: https://forum.netgate.com/topic/173849/jitsi-meet-web-sockets

[^38]: https://jitsi.support/scaling/kubernetes-jitsi-scaling-by-experts

[^39]: https://hub.docker.com/r/h2invent/jitsi-admin-websocket

[^40]: https://github.com/jitsi/docker-jitsi-meet/issues/1106

[^41]: https://artifacthub.io/packages/helm/jitsi-meet/jitsi-meet/1.3.7

[^42]: https://community.traefik.io/t/routers-only-on-websecure-entrypoint-by-default/8154

[^43]: https://community.traefik.io/t/traefik-kubernetes-udp/14460

[^44]: https://community.traefik.io/t/is-it-possible-to-have-a-port-range-for-an-entrypoint/24953

[^45]: https://github.com/jitsi/jitsi-videobridge/issues/1913

[^46]: https://github.com/containous/traefik/issues/6551

[^47]: https://docs.workadventu.re/admin/admin-self-hosting/

[^48]: https://stackoverflow.com/questions/********/k8s-with-traefik-open-udp-port-as-ingress-rule

[^49]: https://github.com/jitsi/docker-jitsi-meet/issues/220

[^50]: https://www.reddit.com/r/k3s/comments/uqwgxp/traefik_v2_added_entrypoint_but_not_available/

[^51]: https://github.com/jitsi/docker-jitsi-meet/issues/1959

[^52]: https://github.com/jitsi/docker-jitsi-meet/issues/989

[^53]: https://doc.traefik.io/traefik/v2.2/routing/providers/kubernetes-crd/

[^54]: https://itforvn.com/jitisi-meet-phan-1-xay-dung-he-thong-jitsi-meeting-cho-doanh-nghiep/

[^55]: https://github.com/traefik/traefik/issues/10998

